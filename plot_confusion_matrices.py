import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import os

def load_confusion_matrix_data(model_path):
    """加载混淆矩阵数据"""
    json_file = None
    for root, dirs, files in os.walk(model_path):
        for file in files:
            if file == 'snr_confusion_matrices.json':
                json_file = os.path.join(root, file)
                break
        if json_file:
            break
    
    if not json_file:
        print(f"未找到 {model_path} 的混淆矩阵数据文件")
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data

def normalize_confusion_matrix(cm):
    """将混淆矩阵归一化为百分比"""
    cm = np.array(cm)
    # 按行归一化（每行代表真实类别）
    row_sums = cm.sum(axis=1, keepdims=True)
    # 避免除零
    row_sums[row_sums == 0] = 1
    normalized_cm = cm / row_sums
    return normalized_cm

def plot_confusion_matrices_grid(snr_values, output_dir):
    """绘制混淆矩阵网格图"""
    # 模型名称和对应的文件夹
    models = {
        'AWN': '混淆矩阵/awn',
        'CLDNN': '混淆矩阵/cldnn',
        'MCNET': '混淆矩阵/mamc',  # MAMC对应MCNET
        'MCLDNN': '混淆矩阵/mawdn',  # MAWDN对应MCLDNN
        'MWRNN': '混淆矩阵/mwrnn'  # MWRNN使用自己的名字
    }
    
    # 加载所有模型的数据
    model_data = {}
    class_names = None
    
    for model_name, model_path in models.items():
        data = load_confusion_matrix_data(model_path)
        if data is not None:
            model_data[model_name] = data
            if class_names is None:
                class_names = data['metadata']['class_names']
    
    if not model_data:
        print("未找到任何有效的混淆矩阵数据")
        return
    
    # 为每个SNR值创建一个图
    for snr in snr_values:
        fig, axes = plt.subplots(1, 5, figsize=(20, 4))
        fig.suptitle(f'Confusion Matrices at SNR = {snr}dB', fontsize=16, fontweight='bold')
        
        for idx, (model_name, data) in enumerate(model_data.items()):
            snr_key = f'SNR_{snr}dB'
            
            if snr_key in data['confusion_matrices']:
                cm = data['confusion_matrices'][snr_key]['confusion_matrix']
                normalized_cm = normalize_confusion_matrix(cm)
                
                # 绘制热图
                im = axes[idx].imshow(normalized_cm, cmap='Blues', aspect='auto', vmin=0, vmax=1)
                
                # 设置标题
                axes[idx].set_title(model_name, fontsize=12, fontweight='bold')
                
                # 设置坐标轴标签
                axes[idx].set_xticks(range(len(class_names)))
                axes[idx].set_yticks(range(len(class_names)))
                axes[idx].set_xticklabels(class_names, rotation=45, ha='right', fontsize=8)
                axes[idx].set_yticklabels(class_names, fontsize=8)
                
                # 只在第一个子图显示y轴标签
                if idx == 0:
                    axes[idx].set_ylabel('Actual class', fontsize=10)
                
                # 在最后一个子图下方显示x轴标签
                if idx == len(model_data) - 1:
                    axes[idx].set_xlabel('Predict class', fontsize=10)
                
                # 添加数值标注（只显示对角线和较大的值）
                for i in range(len(class_names)):
                    for j in range(len(class_names)):
                        value = normalized_cm[i, j]
                        if value > 0.1 or i == j:  # 只显示大于0.1的值或对角线值
                            text_color = 'white' if value > 0.5 else 'black'
                            axes[idx].text(j, i, f'{value:.2f}', 
                                         ha='center', va='center', 
                                         color=text_color, fontsize=6)
            else:
                # 如果没有该SNR的数据，显示空白
                axes[idx].text(0.5, 0.5, f'No data\nfor {snr}dB', 
                             ha='center', va='center', transform=axes[idx].transAxes)
                axes[idx].set_title(model_name, fontsize=12, fontweight='bold')
        
        # 添加颜色条
        cbar = fig.colorbar(im, ax=axes, shrink=0.8, aspect=20)
        cbar.set_label('Accuracy', rotation=270, labelpad=15)
        
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(output_dir, f'confusion_matrices_SNR_{snr}dB.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"已保存: {output_path}")
        
        plt.close()

def main():
    """主函数"""
    # 要绘制的SNR值
    snr_values = [0, 10]
    
    # 输出目录
    output_dir = '混淆矩阵'
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 绘制混淆矩阵网格图
    plot_confusion_matrices_grid(snr_values, output_dir)
    
    print("混淆矩阵可视化完成！")

if __name__ == "__main__":
    main()
